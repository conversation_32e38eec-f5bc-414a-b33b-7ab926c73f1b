#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os
import numpy as np
import platform
from matplotlib.font_manager import FontProperties

# 导入分析模块
from analyze_lottery import load_data, calculate_stats

class LotteryAnalyzerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("彩票数据分析工具")
        self.root.geometry("1200x800")
        
        # 设置中文字体
        self.setup_fonts()
        
        # 数据变量
        self.df = None
        self.file_path = ""
        
        # 创建界面
        self.create_widgets()
        
    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 检测操作系统
            os_type = platform.system()
            
            if os_type == 'Darwin':  # macOS
                # 尝试使用多种macOS中文字体
                font_paths = [
                    '/System/Library/Fonts/PingFang.ttc',
                    '/System/Library/Fonts/STHeiti Light.ttc',
                    '/System/Library/Fonts/STHeiti Medium.ttc',
                    '/Library/Fonts/Microsoft/SimHei.ttf',
                    '/Library/Fonts/Arial Unicode.ttf'
                ]
                
                font_found = False
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        self.font = FontProperties(fname=font_path)
                        plt.rcParams['font.family'] = ['sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False
                        font_found = True
                        break
                
                if not font_found:
                    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'STHeiti']
            
            elif os_type == 'Linux':
                # Linux系统中文字体设置
                plt.rcParams['font.sans-serif'] = ['SimHei', 'WenQuanYi Micro Hei', 'DejaVu Sans']
            
            else:  # Windows或其他系统
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
                
            plt.rcParams['axes.unicode_minus'] = False
        except:
            print("警告: 设置中文字体失败，图表中的中文可能显示不正确")
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件选择
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(file_frame, text="数据文件:").pack(side=tk.LEFT, padx=5)
        self.file_entry = ttk.Entry(file_frame, width=50)
        self.file_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.file_entry.insert(0, "开奖历史.csv")
        
        ttk.Button(file_frame, text="浏览...", command=self.browse_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="加载数据", command=self.load_data_file).pack(side=tk.LEFT, padx=5)
        
        # 分析选项
        options_frame = ttk.Frame(control_frame)
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 位置选择
        ttk.Label(options_frame, text="分析位置:").pack(side=tk.LEFT, padx=5)
        self.position_var = tk.IntVar(value=6)
        position_combo = ttk.Combobox(options_frame, textvariable=self.position_var, width=5)
        position_combo['values'] = [1, 2, 3, 4, 5, 6, 7]
        position_combo.pack(side=tk.LEFT, padx=5)
        
        # 分析列选择
        ttk.Label(options_frame, text="分析项目:").pack(side=tk.LEFT, padx=5)
        self.analyze_opencode_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="号码", variable=self.analyze_opencode_var).pack(side=tk.LEFT, padx=5)
        
        self.analyze_wave_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="波色", variable=self.analyze_wave_var).pack(side=tk.LEFT, padx=5)
        
        self.analyze_zodiac_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="生肖", variable=self.analyze_zodiac_var).pack(side=tk.LEFT, padx=5)
        
        # 最近期数
        ttk.Label(options_frame, text="最近期数:").pack(side=tk.LEFT, padx=5)
        self.recent_var = tk.IntVar(value=30)
        recent_entry = ttk.Entry(options_frame, textvariable=self.recent_var, width=5)
        recent_entry.pack(side=tk.LEFT, padx=5)
        
        # 分析按钮
        ttk.Button(options_frame, text="分析数据", command=self.analyze_data).pack(side=tk.LEFT, padx=20)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建选项卡页面
        self.tab_opencode = ttk.Frame(self.notebook)
        self.tab_wave = ttk.Frame(self.notebook)
        self.tab_zodiac = ttk.Frame(self.notebook)
        
        self.notebook.add(self.tab_opencode, text="号码统计")
        self.notebook.add(self.tab_wave, text="波色统计")
        self.notebook.add(self.tab_zodiac, text="生肖统计")
        
        # 状态栏
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_var.set("就绪")
    
    def browse_file(self):
        """浏览文件对话框"""
        file_path = filedialog.askopenfilename(
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if file_path:
            self.file_entry.delete(0, tk.END)
            self.file_entry.insert(0, file_path)
    
    def load_data_file(self):
        """加载数据文件"""
        file_path = self.file_entry.get()
        if not file_path:
            messagebox.showerror("错误", "请选择数据文件")
            return
        
        try:
            self.status_var.set(f"正在加载数据文件: {file_path}")
            self.root.update()
            
            self.df = load_data(file_path)
            self.file_path = file_path
            
            self.status_var.set(f"数据加载成功: {len(self.df)} 条记录")
            messagebox.showinfo("成功", f"成功加载 {len(self.df)} 条记录")
        except Exception as e:
            self.status_var.set(f"加载数据失败: {str(e)}")
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")
    
    def analyze_data(self):
        """分析数据"""
        if self.df is None:
            messagebox.showerror("错误", "请先加载数据文件")
            return
        
        position = self.position_var.get()
        # 转换为0-based索引
        position_idx = position - 1
        
        # 检查选择的分析项目
        columns_to_analyze = []
        if self.analyze_opencode_var.get():
            columns_to_analyze.append("openCode")
        if self.analyze_wave_var.get():
            columns_to_analyze.append("wave")
        if self.analyze_zodiac_var.get():
            columns_to_analyze.append("zodiac")
        
        if not columns_to_analyze:
            messagebox.showerror("错误", "请至少选择一项分析内容")
            return
        
        recent_periods = self.recent_var.get()
        
        self.status_var.set("正在分析数据...")
        self.root.update()
        
        try:
            # 如果位置不是默认的第7位(索引6)，则创建新列
            if position_idx != 6:
                for col in ['openCode', 'wave', 'zodiac']:
                    col_name = f'{col}_{position}'
                    self.df[col_name] = self.df[col].apply(
                        lambda x: x.split(',')[position_idx].strip('"').strip() if len(x.split(',')) > position_idx else None
                    )
            
            # 清空选项卡内容
            for widget in self.tab_opencode.winfo_children():
                widget.destroy()
            for widget in self.tab_wave.winfo_children():
                widget.destroy()
            for widget in self.tab_zodiac.winfo_children():
                widget.destroy()
            
            # 分析并显示结果
            if "openCode" in columns_to_analyze:
                self.analyze_and_display("openCode", position, recent_periods, self.tab_opencode)
            
            if "wave" in columns_to_analyze:
                self.analyze_and_display("wave", position, recent_periods, self.tab_wave)
            
            if "zodiac" in columns_to_analyze:
                self.analyze_and_display("zodiac", position, recent_periods, self.tab_zodiac)
            
            self.status_var.set("分析完成")
        except Exception as e:
            self.status_var.set(f"分析数据失败: {str(e)}")
            messagebox.showerror("错误", f"分析数据失败: {str(e)}")
    
    def analyze_and_display(self, column, position, recent_periods, tab):
        """分析指定列并在选项卡中显示结果"""
        column_name = f"{column}_{position}"
        column_label = f"{column}第{position}位"
        
        # 创建框架
        frame = ttk.Frame(tab)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格和图表的分隔窗口
        paned_window = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格框架
        table_frame = ttk.Frame(paned_window)
        paned_window.add(table_frame, weight=1)
        
        # 创建图表框架
        chart_frame = ttk.Frame(paned_window)
        paned_window.add(chart_frame, weight=2)
        
        # 创建表格选项卡
        table_notebook = ttk.Notebook(table_frame)
        table_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 总体统计
        total_tab = ttk.Frame(table_notebook)
        table_notebook.add(total_tab, text="总体统计")
        
        # 当年统计
        year_tab = ttk.Frame(table_notebook)
        table_notebook.add(year_tab, text=f"{datetime.datetime.now().year}年统计")
        
        # 当月统计
        month_tab = ttk.Frame(table_notebook)
        table_notebook.add(month_tab, text=f"{datetime.datetime.now().month}月统计")
        
        # 最近N期统计
        recent_tab = ttk.Frame(table_notebook)
        table_notebook.add(recent_tab, text=f"最近{recent_periods}期")
        
        # 计算统计数据
        # 1. 总体统计
        total_counts, total_intervals = calculate_stats(self.df, column_name)
        self.create_stat_table(total_tab, total_counts, total_intervals)
        
        # 2. 当年统计
        current_year = datetime.datetime.now().year
        year_filter = self.df['year'] == current_year
        year_counts, year_intervals = calculate_stats(self.df, column_name, year_filter)
        if year_counts:
            self.create_stat_table(year_tab, year_counts, year_intervals)
        
        # 3. 当月统计
        current_month = datetime.datetime.now().month
        month_filter = (self.df['year'] == current_year) & (self.df['month'] == current_month)
        month_counts, month_intervals = calculate_stats(self.df, column_name, month_filter)
        if month_counts:
            self.create_stat_table(month_tab, month_counts, month_intervals)
        
        # 4. 最近N期统计
        recent_filter = self.df['序号'] >= self.df['序号'].max() - (recent_periods - 1)
        recent_counts, recent_intervals = calculate_stats(self.df, column_name, recent_filter)
        if recent_counts:
            self.create_stat_table(recent_tab, recent_counts, recent_intervals)
        
        # 创建图表
        self.create_chart(chart_frame, total_counts, total_intervals, column_label)
    
    def create_stat_table(self, parent, counts, intervals):
        """创建统计表格"""
        # 创建表格
        columns = ("值", "出现次数", "当前间隔")
        tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=80, anchor=tk.CENTER)
        
        # 添加数据
        for val, count in sorted(counts.items(), key=lambda x: x[1], reverse=True):
            tree.insert("", tk.END, values=(val, count, intervals[val]))
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_chart(self, parent, counts, intervals, title_prefix):
        """创建统计图表"""
        # 按照出现次数排序
        sorted_items = sorted(counts.items(), key=lambda x: x[1], reverse=True)
        labels = [item[0] for item in sorted_items]
        count_values = [item[1] for item in sorted_items]
        
        # 对应的当前间隔
        current_interval_values = [intervals[label] for label in labels]
        
        # 创建图表
        fig = plt.Figure(figsize=(10, 8), dpi=100)
        
        # 出现次数图
        ax1 = fig.add_subplot(211)
        x = np.arange(len(labels))
        ax1.bar(x, count_values, width=0.6)
        ax1.set_ylabel('出现次数', fontproperties=self.font if hasattr(self, 'font') else None)
        ax1.set_title(f'{title_prefix}出现次数', fontproperties=self.font if hasattr(self, 'font') else None)
        ax1.set_xticks(x)
        ax1.set_xticklabels(labels, fontproperties=self.font if hasattr(self, 'font') else None)
        
        # 在柱状图上显示具体数值
        for i, v in enumerate(count_values):
            ax1.text(i, v + 0.1, str(v), ha='center')
        
        # 当前间隔图
        ax2 = fig.add_subplot(212)
        ax2.bar(x, current_interval_values, width=0.6)
        ax2.set_ylabel('间隔期数', fontproperties=self.font if hasattr(self, 'font') else None)
        ax2.set_title(f'{title_prefix}当前间隔', fontproperties=self.font if hasattr(self, 'font') else None)
        ax2.set_xticks(x)
        ax2.set_xticklabels(labels, fontproperties=self.font if hasattr(self, 'font') else None)
        
        # 在柱状图上显示具体数值
        for i, v in enumerate(current_interval_values):
            ax2.text(i, v + 0.1, str(v), ha='center')
        
        fig.tight_layout()
        
        # 在Tkinter窗口中显示图表
        canvas = FigureCanvasTkAgg(fig, master=parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

if __name__ == "__main__":
    import datetime  # 添加datetime模块导入
    
    root = tk.Tk()
    app = LotteryAnalyzerApp(root)
    root.mainloop() 