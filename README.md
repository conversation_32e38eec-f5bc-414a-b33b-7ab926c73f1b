# 彩票数据分析工具

这个Python脚本用于分析彩票开奖历史数据，特别是对指定位置的号码、波色和生肖进行统计分析。

## 功能特点

- 分析指定位置的数据（可选择第1-7位）
- 统计号码、波色和生肖的历史出现次数
- 计算平均间隔和当前间隔期数
- 按总体、年度、月度和最近N期进行多维度分析
- 生成可视化统计图表

## 使用方法

```bash
python analyze_lottery.py [选项]
```

### 命令行选项

- `-f, --file`：指定CSV数据文件路径，默认为"开奖历史.csv"
- `-p, --position`：指定分析的位置索引（0-6，对应第1-7位），默认为6（第7位）
- `-c, --columns`：指定要分析的列，可以是"openCode"、"wave"和"zodiac"的任意组合，默认全部分析
- `-n, --recent`：指定最近期数的统计范围，默认30期

### 使用示例

1. 使用默认设置分析第7位的所有数据：
   ```bash
   python analyze_lottery.py
   ```

2. 仅分析第1位的波色（wave）数据：
   ```bash
   python analyze_lottery.py --position 0 --columns wave
   ```

3. 分析第3位的号码（openCode）和生肖（zodiac），并统计最近50期：
   ```bash
   python analyze_lottery.py -p 2 -c openCode zodiac -n 50
   ```

4. 指定不同的数据文件进行分析：
   ```bash
   python analyze_lottery.py --file 其他数据.csv
   ```

## 输出内容

1. 控制台输出：包含总体、年度、月度和最近N期的统计信息
   - 出现次数
   - 平均间隔
   - 当前间隔

2. 图形输出：为每个分析列生成PNG格式的统计图表
   - 出现次数柱状图
   - 平均间隔和当前间隔对比图

## 系统要求

- Python 3.6+
- 依赖库：
  - pandas：用于数据处理
  - matplotlib：用于图形可视化
  - numpy：用于数值计算

## 安装依赖

```bash
pip install pandas matplotlib numpy
``` 