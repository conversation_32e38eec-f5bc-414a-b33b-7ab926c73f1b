#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import datetime
from collections import Counter
import os
import numpy as np
from matplotlib.font_manager import FontProperties
import platform
import argparse

# 设置中文字体
try:
    # 检测操作系统
    os_type = platform.system()
    
    if os_type == 'Darwin':  # macOS
        # 尝试使用多种macOS中文字体
        font_paths = [
            '/System/Library/Fonts/PingFang.ttc',
            '/System/Library/Fonts/STHeiti Light.ttc',
            '/System/Library/Fonts/STHeiti Medium.ttc',
            '/Library/Fonts/Microsoft/SimHei.ttf',
            '/Library/Fonts/Arial Unicode.ttf'
        ]
        
        font_found = False
        for font_path in font_paths:
            if os.path.exists(font_path):
                font = FontProperties(fname=font_path)
                plt.rcParams['font.family'] = ['sans-serif']
                plt.rcParams['axes.unicode_minus'] = False
                print(f"使用字体: {font_path}")
                font_found = True
                break
        
        if not font_found:
            print("警告: 未找到中文字体文件，尝试使用系统默认字体")
            plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'STHeiti']
    
    elif os_type == 'Linux':
        # Linux系统中文字体设置
        plt.rcParams['font.sans-serif'] = ['SimHei', 'WenQuanYi Micro Hei', 'DejaVu Sans']
    
    else:  # Windows或其他系统
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告: 设置中文字体失败，图表中的中文可能显示不正确")

def load_data(file_path):
    """加载CSV数据并处理"""
    df = pd.read_csv(file_path)
    
    # 处理日期列
    df['openTime'] = pd.to_datetime(df['openTime'])
    df['year'] = df['openTime'].dt.year
    df['month'] = df['openTime'].dt.month
    
    # 提取第七个位置的数据
    df['openCode_7'] = df['openCode'].apply(lambda x: x.split(',')[6].strip('"'))
    df['wave_7'] = df['wave'].apply(lambda x: x.split(',')[6].strip('"'))
    df['zodiac_7'] = df['zodiac'].apply(lambda x: x.split(',')[6].strip('"').strip())
    
    return df

def calculate_stats(df, column, filter_condition=None):
    """计算指定列的统计数据"""
    if filter_condition is not None:
        filtered_df = df[filter_condition]
    else:
        filtered_df = df
    
    if len(filtered_df) == 0:
        return None, None
    
    # 计算出现次数
    value_counts = filtered_df[column].value_counts().to_dict()
    
    # 计算当前间隔 (距上次出现的期数)
    current_intervals = {}
    latest_idx = filtered_df['序号'].max()
    
    unique_values = filtered_df[column].unique()
    for val in unique_values:
        last_occurrence = filtered_df[filtered_df[column] == val]['序号'].max()
        current_intervals[val] = latest_idx - last_occurrence
    
    return value_counts, current_intervals

def generate_report(df, column_name, column_label, recent_periods=30):
    """生成指定列的分析报告"""
    print(f"\n========== {column_label}统计 ==========")
    
    # 当前日期
    current_year = datetime.datetime.now().year
    current_month = datetime.datetime.now().month
    
    # 1. 总体统计
    counts, current_intervals = calculate_stats(df, column_name)
    print("\n--- 总体统计 ---")
    for val, count in sorted(counts.items(), key=lambda x: x[1], reverse=True):
        print(f"{val}: 出现{count}次, 当前间隔{current_intervals[val]}期")
    
    # 2. 当年统计
    year_filter = df['year'] == current_year
    year_counts, year_current_intervals = calculate_stats(df, column_name, year_filter)
    if year_counts:
        print(f"\n--- {current_year}年统计 ---")
        for val, count in sorted(year_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"{val}: 出现{count}次, 当前间隔{year_current_intervals[val]}期")
    
    # 3. 当月统计
    month_filter = (df['year'] == current_year) & (df['month'] == current_month)
    month_counts, month_current_intervals = calculate_stats(df, column_name, month_filter)
    if month_counts:
        print(f"\n--- {current_year}年{current_month}月统计 ---")
        for val, count in sorted(month_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"{val}: 出现{count}次, 当前间隔{month_current_intervals[val]}期")
    
    # 4. 最近N期统计
    recent_filter = df['序号'] >= df['序号'].max() - (recent_periods - 1)
    recent_counts, recent_current_intervals = calculate_stats(df, column_name, recent_filter)
    if recent_counts:
        print(f"\n--- 最近{recent_periods}期统计 ---")
        for val, count in sorted(recent_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"{val}: 出现{count}次, 当前间隔{recent_current_intervals[val]}期")
    
    # 生成可视化图表
    plot_stats(counts, current_intervals, column_label)
    
    return counts, current_intervals

def plot_stats(counts, current_intervals, title_prefix):
    """生成统计图表"""
    # 按照出现次数排序
    sorted_items = sorted(counts.items(), key=lambda x: x[1], reverse=True)
    labels = [item[0] for item in sorted_items]
    count_values = [item[1] for item in sorted_items]
    
    # 对应的当前间隔
    current_interval_values = [current_intervals[label] for label in labels]
    
    # 尝试获取字体
    try:
        if 'font' in globals():
            prop = font
        else:
            # 尝试使用系统可用的中文字体
            prop = FontProperties(family=['SimHei', 'STHeiti', 'Arial Unicode MS'])
    except:
        prop = None
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 出现次数图
    x = np.arange(len(labels))
    ax1.bar(x, count_values, width=0.6)
    ax1.set_ylabel('出现次数', fontproperties=prop)
    ax1.set_title(f'{title_prefix}出现次数', fontproperties=prop)
    ax1.set_xticks(x)
    ax1.set_xticklabels(labels, fontproperties=prop)
    
    # 在柱状图上显示具体数值
    for i, v in enumerate(count_values):
        ax1.text(i, v + 0.1, str(v), ha='center')
    
    # 当前间隔图
    ax2.bar(x, current_interval_values, width=0.6)
    ax2.set_ylabel('间隔期数', fontproperties=prop)
    ax2.set_title(f'{title_prefix}当前间隔', fontproperties=prop)
    ax2.set_xticks(x)
    ax2.set_xticklabels(labels, fontproperties=prop)
    
    # 在柱状图上显示具体数值
    for i, v in enumerate(current_interval_values):
        ax2.text(i, v + 0.1, str(v), ha='center')
    
    plt.tight_layout()
    
    # 保存图表
    output_file = f'{title_prefix}_statistics.png'
    plt.savefig(output_file)
    print(f"统计图已保存为: {output_file}")
    
    # 关闭图表，释放内存
    plt.close()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='彩票开奖数据分析工具')
    parser.add_argument('-f', '--file', default='开奖历史.csv', help='CSV文件路径')
    parser.add_argument('-p', '--position', type=int, default=6, help='要分析的位置索引（从0开始计数）')
    parser.add_argument('-c', '--columns', nargs='+', default=['openCode', 'wave', 'zodiac'], 
                        help='要分析的列，可包括: openCode, wave, zodiac')
    parser.add_argument('-n', '--recent', type=int, default=30, help='最近期数')
    
    args = parser.parse_args()
    
    print(f"正在分析文件: {args.file}")
    print(f"分析位置: 第{args.position+1}位")
    print(f"分析列: {', '.join(args.columns)}")
    print(f"最近期数: {args.recent}期")
    print("-" * 30)
    
    # 加载数据
    df = load_data(args.file)
    
    # 如果位置不是默认的第7位(索引6)，则创建新列
    if args.position != 6:
        for col in ['openCode', 'wave', 'zodiac']:
            df[f'{col}_{args.position+1}'] = df[col].apply(lambda x: x.split(',')[args.position].strip('"') if len(x.split(',')) > args.position else None)
    
    # 生成报告
    for column in args.columns:
        col_name = f'{column}_{args.position+1}'
        generate_report(df, col_name, f'{column}第{args.position+1}位', args.recent)

if __name__ == "__main__":
    main() 