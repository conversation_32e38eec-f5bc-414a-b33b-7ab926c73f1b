# 彩票数据分析工具使用说明

## 功能概述

这个工具可以自动载入CSV文件并分析彩票开奖数据，提供详细的统计信息和可视化图表。

## 主要特性

✅ **自动载入CSV文件** - 无需手动指定文件路径  
✅ **智能文件选择** - 自动搜索当前目录中的CSV文件  
✅ **多种分析模式** - 支持快速分析和自定义分析  
✅ **详细统计报告** - 提供总体、年度、月度和最近期数统计  
✅ **可视化图表** - 自动生成统计图表并保存为PNG文件  
✅ **中文字体支持** - 自动检测并使用系统中文字体  

## 使用方法

### 1. 快速分析模式（推荐）

直接运行程序，无需任何参数：

```bash
python3 analyze_lottery.py
```

**特点：**
- 自动搜索当前目录中的CSV文件
- 如果只有一个CSV文件，自动载入
- 如果有多个CSV文件，提供选择菜单
- 默认分析第7位的开奖号码、波色、生肖数据
- 分析最近30期的统计信息

### 2. 自动搜索模式

强制启用自动搜索功能：

```bash
python3 analyze_lottery.py --auto
```

### 3. 自定义分析模式

使用命令行参数进行自定义分析：

```bash
# 指定CSV文件
python3 analyze_lottery.py -f 数据文件.csv

# 分析第1位（索引0）的开奖号码
python3 analyze_lottery.py -p 0 -c openCode

# 分析最近50期数据
python3 analyze_lottery.py -n 50

# 分析多个列
python3 analyze_lottery.py -c openCode wave zodiac
```

## 命令行参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-f, --file` | 指定CSV文件路径 | 自动搜索 |
| `-p, --position` | 分析位置（从0开始计数） | 6（第7位） |
| `-c, --columns` | 要分析的列 | openCode, wave, zodiac |
| `-n, --recent` | 最近期数统计 | 30 |
| `--auto` | 强制启用自动搜索模式 | - |
| `-h, --help` | 显示帮助信息 | - |

## 输出说明

### 统计报告包含：

1. **总体统计** - 所有历史数据的统计
2. **当年统计** - 当前年份的统计
3. **当月统计** - 当前月份的统计  
4. **最近N期统计** - 最近指定期数的统计

### 每项统计显示：

- **出现次数** - 该值在统计范围内出现的总次数
- **当前间隔** - 距离上次出现的期数

### 生成的文件：

- `openCode第7位_statistics.png` - 开奖号码统计图表
- `wave第7位_statistics.png` - 波色统计图表  
- `zodiac第7位_statistics.png` - 生肖统计图表

## 使用示例

### 示例1：快速分析
```bash
python3 analyze_lottery.py
```
输出：自动载入CSV文件，分析第7位所有数据

### 示例2：分析第1位开奖号码
```bash
python3 analyze_lottery.py -p 0 -c openCode
```

### 示例3：分析最近100期的波色数据
```bash
python3 analyze_lottery.py -c wave -n 100
```

### 示例4：指定文件分析
```bash
python3 analyze_lottery.py -f 历史数据.csv -p 5 -c zodiac
```

## 注意事项

1. **CSV文件格式要求**：
   - 必须包含 `openTime`, `openCode`, `wave`, `zodiac` 等列
   - 数据格式应为逗号分隔的字符串

2. **文件放置**：
   - 将CSV文件放在与程序相同的目录中
   - 或使用 `-f` 参数指定完整路径

3. **系统要求**：
   - Python 3.x
   - 需要安装：pandas, matplotlib, numpy

4. **字体支持**：
   - 程序会自动检测系统中文字体
   - 如果图表中文显示异常，请检查系统字体安装

## 故障排除

**问题：找不到CSV文件**
- 确保CSV文件在当前目录中
- 或使用 `-f` 参数指定正确的文件路径

**问题：图表中文显示异常**
- 检查系统是否安装了中文字体
- macOS用户通常不会有此问题

**问题：数据加载失败**
- 检查CSV文件格式是否正确
- 确保文件没有损坏且可以正常打开

## 更新日志

### v2.0 - 自动载入优化版
- ✅ 新增自动搜索CSV文件功能
- ✅ 新增快速分析模式
- ✅ 改进错误处理和用户提示
- ✅ 优化用户体验和交互界面
- ✅ 新增详细的使用说明和帮助信息
